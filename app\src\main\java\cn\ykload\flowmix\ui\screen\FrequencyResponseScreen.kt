package cn.ykload.flowmix.ui.screen

import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.ExperimentalAnimationApi
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.spring
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.scaleIn
import androidx.compose.animation.scaleOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.animation.with
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.AutoFixHigh
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.Save
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ExposedDropdownMenuBox
import androidx.compose.material3.ExposedDropdownMenuDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextField
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import cn.ykload.flowmix.data.AutoEqData
import cn.ykload.flowmix.data.DataSource
import cn.ykload.flowmix.data.Headphone
import cn.ykload.flowmix.data.TargetCurve
import cn.ykload.flowmix.ui.component.BottomModalType
import cn.ykload.flowmix.ui.component.BottomToast
import cn.ykload.flowmix.ui.component.FrequencyResponseChart
import cn.ykload.flowmix.viewmodel.FrequencyResponseViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FrequencyResponseScreen(
    autoEqData: AutoEqData? = null,
    isLoudnessCompensationEnabled: Boolean = false,
    modifier: Modifier = Modifier,
    viewModel: FrequencyResponseViewModel,
    hasPageEntered: Boolean = false,
    mainViewModel: cn.ykload.flowmix.viewmodel.MainViewModel? = null
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    // 当AutoEq数据变化时，更新ViewModel
    LaunchedEffect(autoEqData) {
        viewModel.setAutoEqData(autoEqData)
    }

    // 图例高度动画进度
    val legendHeightProgress by animateFloatAsState(
        targetValue = if (hasPageEntered && uiState.currentMeasurementData != null) 1f else 0f,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessMedium
        ),
        label = "legend_height_animation"
    )
    
    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(horizontal = 16.dp)
            .verticalScroll(rememberScrollState()),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {

        // 页面标题
        Text(
            text = "频响",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(vertical = 8.dp)
        )

        // 设备选择区域
        DeviceSelectionCard(
            dataSources = uiState.dataSources,
            selectedDataSource = uiState.selectedDataSource,
            brands = uiState.brands,
            selectedBrand = uiState.selectedBrand,
            headphones = uiState.headphones,
            selectedHeadphone = uiState.selectedHeadphone,
            measurementConditions = uiState.measurementConditions,
            selectedMeasurementCondition = uiState.selectedMeasurementCondition,
            targetCurves = uiState.targetCurves,
            selectedTargetCurve = uiState.selectedTargetCurve,
            isLoading = uiState.isLoading,
            isCompactMode = uiState.isCompactMode,
            onDataSourceSelected = viewModel::selectDataSource,
            onBrandSelected = viewModel::selectBrand,
            onHeadphoneSelected = viewModel::selectHeadphone,
            onMeasurementConditionSelected = viewModel::selectMeasurementCondition,
            onTargetCurveSelected = viewModel::selectTargetCurve,
            onTargetCurveDeleted = viewModel::deleteLocalTargetCurve,
            onRefresh = viewModel::loadDataSources,
            onCardClick = viewModel::expandCard,
            onDropdownStateChanged = viewModel::updateDropdownState
        )

        // 频响图表 - 带动画出现
        AnimatedVisibility(
            visible = uiState.canShowChart,
            enter = slideInVertically(
                initialOffsetY = { it / 3 },
                animationSpec = spring(
                    dampingRatio = Spring.DampingRatioMediumBouncy,
                    stiffness = Spring.StiffnessMedium
                )
            ) + fadeIn(
                animationSpec = tween(400, delayMillis = 200)
            ) + scaleIn(
                initialScale = 0.8f,
                animationSpec = spring(
                    dampingRatio = Spring.DampingRatioMediumBouncy,
                    stiffness = Spring.StiffnessMedium
                )
            ),
            exit = slideOutVertically(
                targetOffsetY = { it / 3 },
                animationSpec = tween(300)
            ) + fadeOut(
                animationSpec = tween(300)
            ) + scaleOut(
                targetScale = 0.8f,
                animationSpec = tween(300)
            )
        ) {
            Column {
                // 图例说明（当有频响数据时总是显示）
                AnimatedVisibility(
                    visible = uiState.currentMeasurementData != null,
                    enter = fadeIn(
                        animationSpec = tween(300, delayMillis = 300)
                    ) + slideInVertically(
                        initialOffsetY = { -it / 4 },
                        animationSpec = spring(
                            dampingRatio = Spring.DampingRatioMediumBouncy,
                            stiffness = Spring.StiffnessMedium
                        )
                    ),
                    exit = fadeOut(animationSpec = tween(200)) + slideOutVertically(
                        targetOffsetY = { -it / 4 },
                        animationSpec = tween(200)
                    )
                ) {
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        shape = RoundedCornerShape(25.dp)
                    ) {
                        // 使用动画高度包装内容
                        val legendContentHeight = 48.dp // 内容的实际高度
                        val animatedContentHeight by animateDpAsState(
                            targetValue = legendContentHeight * legendHeightProgress,
                            animationSpec = spring(
                                dampingRatio = Spring.DampingRatioMediumBouncy,
                                stiffness = Spring.StiffnessMedium
                            ),
                            label = "legend_content_height_animation"
                        )

                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(animatedContentHeight),
                            contentAlignment = Alignment.Center
                        ) {
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(12.dp),
                                horizontalArrangement = Arrangement.Center
                            ) {
                            // 目标曲线图例（如果有选择）
                            if (uiState.selectedTargetCurve != null && mainViewModel != null) {
                                val mainUiState by mainViewModel.uiState.collectAsStateWithLifecycle()
                                Row(
                                    modifier = Modifier.clickable {
                                        mainViewModel.toggleTargetCurveVisibility()
                                    },
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Box(
                                        modifier = Modifier
                                            .size(12.dp)
                                            .background(
                                                Color.White.copy(alpha = if (mainUiState.isTargetCurveVisible) 1f else 0.3f),
                                                RoundedCornerShape(25.dp)
                                            )
                                    )
                                    Spacer(modifier = Modifier.width(4.dp))
                                    Text(
                                        text = "目标曲线",
                                        style = MaterialTheme.typography.bodySmall,
                                        color = MaterialTheme.colorScheme.onSurfaceVariant.copy(
                                            alpha = if (mainUiState.isTargetCurveVisible) 1f else 0.5f
                                        )
                                    )
                                }

                                Spacer(modifier = Modifier.width(16.dp))
                            }

                            // 原始频响图例
                            if (mainViewModel != null) {
                                val mainUiState by mainViewModel.uiState.collectAsStateWithLifecycle()
                                Row(
                                    modifier = Modifier.clickable {
                                        mainViewModel.toggleOriginalCurveVisibility()
                                    },
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Box(
                                        modifier = Modifier
                                            .size(12.dp)
                                            .background(
                                                MaterialTheme.colorScheme.secondary.copy(
                                                    alpha = if (mainUiState.isOriginalCurveVisible) 1f else 0.3f
                                                ),
                                                RoundedCornerShape(25.dp)
                                            )
                                    )
                                    Spacer(modifier = Modifier.width(4.dp))
                                    Text(
                                        text = "原始频响",
                                        style = MaterialTheme.typography.bodySmall,
                                        color = MaterialTheme.colorScheme.onSurfaceVariant.copy(
                                            alpha = if (mainUiState.isOriginalCurveVisible) 1f else 0.5f
                                        )
                                    )
                                }
                            }

                            // AutoEq调整后图例（如果有AutoEq数据）
                            if (uiState.autoEqData != null && mainViewModel != null) {
                                val mainUiState by mainViewModel.uiState.collectAsStateWithLifecycle()
                                Spacer(modifier = Modifier.width(16.dp))

                                Row(
                                    modifier = Modifier.clickable {
                                        mainViewModel.toggleAutoEqCurveVisibility()
                                    },
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Box(
                                        modifier = Modifier
                                            .size(12.dp)
                                            .background(
                                                MaterialTheme.colorScheme.primary.copy(
                                                    alpha = if (mainUiState.isAutoEqCurveVisible) 1f else 0.3f
                                                ),
                                                RoundedCornerShape(25.dp)
                                            )
                                    )
                                    Spacer(modifier = Modifier.width(4.dp))
                                    Text(
                                        text = "AutoEq调整后",
                                        style = MaterialTheme.typography.bodySmall,
                                        color = MaterialTheme.colorScheme.onSurfaceVariant.copy(
                                            alpha = if (mainUiState.isAutoEqCurveVisible) 1f else 0.5f
                                        )
                                    )
                                }
                            }
                        }
                        }
                    }
                }

                AnimatedVisibility(
                    visible = uiState.currentMeasurementData != null,
                    enter = fadeIn(animationSpec = tween(200, delayMillis = 100)),
                    exit = fadeOut(animationSpec = tween(200))
                ) {
                    Spacer(modifier = Modifier.height(8.dp))
                }

                if (mainViewModel != null) {
                    val mainUiState by mainViewModel.uiState.collectAsStateWithLifecycle()
                    FrequencyResponseChart(
                        measurementData = uiState.currentMeasurementData,
                        autoEqData = uiState.autoEqData,
                        targetData = uiState.currentTargetData,
                        isLoudnessCompensationEnabled = isLoudnessCompensationEnabled,
                        modifier = Modifier.fillMaxWidth(),
                        height = 220.dp, // 与EQ图表保持相同高度
                        showChartLabel = true,
                        chartLabelText = "Frequency Response",
                        onPageEntered = hasPageEntered,
                        enableHeightAnimation = true, // 启用高度动画
                        // 曲线可见性状态
                        isOriginalCurveVisible = mainUiState.isOriginalCurveVisible,
                        isAutoEqCurveVisible = mainUiState.isAutoEqCurveVisible,
                        isTargetCurveVisible = mainUiState.isTargetCurveVisible,
                        // 图例点击回调
                        onOriginalCurveToggle = mainViewModel::toggleOriginalCurveVisibility,
                        onAutoEqCurveToggle = mainViewModel::toggleAutoEqCurveVisibility,
                        onTargetCurveToggle = mainViewModel::toggleTargetCurveVisibility,
                        onClick = viewModel::expandCard
                    )
                } else {
                    // 如果没有mainViewModel，使用默认状态
                    FrequencyResponseChart(
                        measurementData = uiState.currentMeasurementData,
                        autoEqData = uiState.autoEqData,
                        targetData = uiState.currentTargetData,
                        isLoudnessCompensationEnabled = isLoudnessCompensationEnabled,
                        modifier = Modifier.fillMaxWidth(),
                        height = 220.dp,
                        showChartLabel = true,
                        chartLabelText = "Frequency Response",
                        onPageEntered = hasPageEntered,
                        enableHeightAnimation = true,
                        onClick = viewModel::expandCard
                    )
                }
            }
        }

        // 操作按钮区域 - 保存为目标曲线和FlowEq按钮
        if (mainViewModel != null) {
            val mainUiState by mainViewModel.uiState.collectAsStateWithLifecycle()

            ActionButtonsCard(
                canSaveAsTargetCurve = uiState.currentMeasurementData != null &&
                                     uiState.selectedHeadphone != null &&
                                     uiState.selectedMeasurementCondition != null,
                canUseFlowEq = uiState.currentMeasurementData != null && uiState.currentTargetData != null,
                isFlowEqProcessing = mainUiState.isFlowEqProcessing,
                onSaveAsTargetCurve = viewModel::saveCurrentAsTargetCurve,
                onFlowEq = { originalData, targetData ->
                    mainViewModel.performFlowEq(originalData, targetData)
                },
                currentMeasurementData = uiState.currentMeasurementData,
                currentTargetData = uiState.currentTargetData
            )
        }

        // 免责声明卡片 - 文本居中
        Card(
            modifier = Modifier.fillMaxWidth(),
            shape = RoundedCornerShape(25.dp)
        ) {
            Column(
                modifier = Modifier
                    .padding(16.dp)
                    .fillMaxWidth(),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {

                Text(
                    text = "数据来源于 Realab/HuiHiFi，",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )

                Spacer(modifier = Modifier.height(2.dp))

                Text(
                    text = "让我们感谢他们的辛勤工作！",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )

                Spacer(modifier = Modifier.height(8.dp))

                Text(
                    text = "部分数据可能并非频响数据，请仔细甄别",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }

        // 底部padding
        Spacer(modifier = Modifier.height(24.dp))
    }
    
    // 错误消息
    BottomToast(
        isVisible = uiState.errorMessage != null,
        message = uiState.errorMessage ?: "",
        type = BottomModalType.ERROR,
        onDismiss = viewModel::clearError
    )
}

@OptIn(ExperimentalMaterial3Api::class, ExperimentalAnimationApi::class)
@Composable
private fun DeviceSelectionCard(
    dataSources: List<DataSource>,
    selectedDataSource: DataSource?,
    brands: List<String>,
    selectedBrand: String?,
    headphones: List<Headphone>,
    selectedHeadphone: Headphone?,
    measurementConditions: List<String>,
    selectedMeasurementCondition: String?,
    targetCurves: List<TargetCurve>,
    selectedTargetCurve: TargetCurve?,
    isLoading: Boolean,
    isCompactMode: Boolean,
    onDataSourceSelected: (DataSource) -> Unit,
    onBrandSelected: (String) -> Unit,
    onHeadphoneSelected: (Headphone) -> Unit,
    onMeasurementConditionSelected: (String) -> Unit,
    onTargetCurveSelected: (TargetCurve?) -> Unit,
    onTargetCurveDeleted: (TargetCurve) -> Unit,
    onRefresh: () -> Unit,
    onCardClick: () -> Unit,
    onDropdownStateChanged: (Boolean) -> Unit,
    modifier: Modifier = Modifier
) {
    // 跟踪所有下拉菜单的展开状态
    var dataSourceExpanded by remember { mutableStateOf(false) }
    var brandExpanded by remember { mutableStateOf(false) }
    var headphoneExpanded by remember { mutableStateOf(false) }
    var measurementConditionExpanded by remember { mutableStateOf(false) }
    var targetCurveExpanded by remember { mutableStateOf(false) }

    // 计算是否有任何下拉菜单展开
    val isAnyDropdownExpanded = dataSourceExpanded || brandExpanded || headphoneExpanded ||
                               measurementConditionExpanded || targetCurveExpanded

    // 当下拉菜单状态改变时通知ViewModel
    LaunchedEffect(isAnyDropdownExpanded) {
        onDropdownStateChanged(isAnyDropdownExpanded)
    }

    // 计算Card的目标高度
    val targetHeight = remember(selectedDataSource, selectedBrand, selectedHeadphone, selectedMeasurementCondition, selectedTargetCurve, isCompactMode) {
        if (isCompactMode) {
            // 紧凑模式：根据实际选择的内容动态计算高度
            val basePadding = 32.dp // Card的上下padding (16dp * 2)
            val lineHeight = 20.dp // 每行文本的高度
            val lineSpacing = 4.dp // 行间距

            // 计算实际需要显示的行数
            var visibleLines = 0
            if (selectedDataSource != null) visibleLines++
            if (selectedBrand != null) visibleLines++
            if (selectedHeadphone != null) visibleLines++
            if (selectedMeasurementCondition != null) visibleLines++
            if (selectedTargetCurve != null) visibleLines++

            // 至少显示一行（数据源）
            if (visibleLines == 0) visibleLines = 1

            // 计算总高度：基础padding + 行数 * 行高 + (行数-1) * 行间距
            basePadding + (lineHeight * visibleLines) + (lineSpacing * (visibleLines - 1))
        } else {
            // 正常模式
            val baseHeight = 88.dp // Card padding + 数据源选择行
            when {
                selectedMeasurementCondition != null -> baseHeight + 66.dp * 4 // 品牌+耳机+测量条件+目标曲线
                selectedHeadphone != null -> baseHeight + 66.dp * 3 // 品牌+耳机+测量条件
                selectedBrand != null -> baseHeight + 66.dp * 2     // 品牌+耳机
                selectedDataSource != null -> baseHeight + 66.dp    // 品牌
                else -> baseHeight                                  // 只有数据源
            }
        }
    }

    // 高度动画
    val animatedHeight by animateDpAsState(
        targetValue = targetHeight,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessMedium
        ),
        label = "card_height_animation"
    )

    Box(
        modifier = modifier.fillMaxWidth()
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .height(animatedHeight)
                .then(
                    if (isCompactMode) {
                        Modifier.clickable { onCardClick() }
                    } else {
                        Modifier
                    }
                ),
            shape = RoundedCornerShape(25.dp)
        ) {
            AnimatedContent(
                targetState = isCompactMode,
                transitionSpec = {
                    fadeIn(animationSpec = tween(300)) with
                    fadeOut(animationSpec = tween(300))
                },
                label = "card_content_animation"
            ) { compactMode ->
                if (compactMode) {
                    // 紧凑模式显示
                    CompactModeContent(
                        selectedDataSource = selectedDataSource,
                        selectedBrand = selectedBrand,
                        selectedHeadphone = selectedHeadphone,
                        selectedMeasurementCondition = selectedMeasurementCondition,
                        selectedTargetCurve = selectedTargetCurve,
                        modifier = Modifier.padding(16.dp)
                    )
                } else {
                    // 正常模式显示
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            // 数据源选择占据大部分空间
                            Box(
                                modifier = Modifier.weight(1f)
                            ) {
                                DataSourceDropdown(
                                    dataSources = dataSources,
                                    selectedDataSource = selectedDataSource,
                                    onDataSourceSelected = onDataSourceSelected,
                                    enabled = dataSources.isNotEmpty() && !isLoading,
                                    expanded = dataSourceExpanded,
                                    onExpandedChange = { dataSourceExpanded = it }
                                )
                            }

                            Spacer(modifier = Modifier.width(8.dp))

                            IconButton(
                                onClick = onRefresh,
                                enabled = !isLoading
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Refresh,
                                    contentDescription = "刷新"
                                )
                            }
                        }

                        Spacer(modifier = Modifier.height(10.dp))

                        // 品牌选择 - 带动画出现
                        AnimatedVisibility(
                            visible = selectedDataSource != null,
                            enter = slideInVertically(
                                initialOffsetY = { -it / 2 },
                                animationSpec = spring(
                                    dampingRatio = Spring.DampingRatioMediumBouncy,
                                    stiffness = Spring.StiffnessMedium
                                )
                            ) + fadeIn(
                                animationSpec = tween(300, delayMillis = 100)
                            ),
                            exit = slideOutVertically(
                                targetOffsetY = { -it / 2 },
                                animationSpec = tween(200)
                            ) + fadeOut(
                                animationSpec = tween(200)
                            )
                        ) {
                            BrandDropdown(
                                brands = brands,
                                selectedBrand = selectedBrand,
                                onBrandSelected = onBrandSelected,
                                enabled = brands.isNotEmpty() && !isLoading,
                                expanded = brandExpanded,
                                onExpandedChange = { brandExpanded = it }
                            )
                        }

                        // 耳机选择 - 带动画出现
                        AnimatedVisibility(
                            visible = selectedBrand != null,
                            enter = slideInVertically(
                                initialOffsetY = { -it / 2 },
                                animationSpec = spring(
                                    dampingRatio = Spring.DampingRatioMediumBouncy,
                                    stiffness = Spring.StiffnessMedium
                                )
                            ) + fadeIn(
                                animationSpec = tween(300, delayMillis = 100)
                            ),
                            exit = slideOutVertically(
                                targetOffsetY = { -it / 2 },
                                animationSpec = tween(200)
                            ) + fadeOut(
                                animationSpec = tween(200)
                            )
                        ) {
                            Column {
                                Spacer(modifier = Modifier.height(10.dp))
                                HeadphoneDropdown(
                                    headphones = headphones,
                                    selectedHeadphone = selectedHeadphone,
                                    onHeadphoneSelected = onHeadphoneSelected,
                                    enabled = headphones.isNotEmpty() && !isLoading,
                                    expanded = headphoneExpanded,
                                    onExpandedChange = { headphoneExpanded = it }
                                )
                            }
                        }

                        // 测量条件选择 - 带动画出现
                        AnimatedVisibility(
                            visible = selectedHeadphone != null,
                            enter = slideInVertically(
                                initialOffsetY = { -it / 2 },
                                animationSpec = spring(
                                    dampingRatio = Spring.DampingRatioMediumBouncy,
                                    stiffness = Spring.StiffnessMedium
                                )
                            ) + fadeIn(
                                animationSpec = tween(300, delayMillis = 150)
                            ),
                            exit = slideOutVertically(
                                targetOffsetY = { -it / 2 },
                                animationSpec = tween(200)
                            ) + fadeOut(
                                animationSpec = tween(200)
                            )
                        ) {
                            Column {
                                Spacer(modifier = Modifier.height(10.dp))
                                MeasurementConditionDropdown(
                                    conditions = measurementConditions,
                                    selectedCondition = selectedMeasurementCondition,
                                    onConditionSelected = onMeasurementConditionSelected,
                                    enabled = measurementConditions.isNotEmpty() && !isLoading,
                                    expanded = measurementConditionExpanded,
                                    onExpandedChange = { measurementConditionExpanded = it }
                                )
                            }
                        }

                        // 目标曲线选择 - 带动画出现
                        AnimatedVisibility(
                            visible = selectedMeasurementCondition != null,
                            enter = slideInVertically(
                                initialOffsetY = { -it / 2 },
                                animationSpec = spring(
                                    dampingRatio = Spring.DampingRatioMediumBouncy,
                                    stiffness = Spring.StiffnessMedium
                                )
                            ) + fadeIn(
                                animationSpec = tween(300, delayMillis = 200)
                            ),
                            exit = slideOutVertically(
                                targetOffsetY = { -it / 2 },
                                animationSpec = tween(200)
                            ) + fadeOut(
                                animationSpec = tween(200)
                            )
                        ) {
                            Column {
                                Spacer(modifier = Modifier.height(10.dp))
                                TargetCurveDropdown(
                                    targetCurves = targetCurves,
                                    selectedTargetCurve = selectedTargetCurve,
                                    onTargetCurveSelected = onTargetCurveSelected,
                                    onTargetCurveDeleted = onTargetCurveDeleted,
                                    enabled = targetCurves.isNotEmpty() && !isLoading,
                                    expanded = targetCurveExpanded,
                                    onExpandedChange = { targetCurveExpanded = it }
                                )
                            }
                        }
                    }
                }
            }
        }

        // 加载时的模糊遮罩和指示器 - 带动画和防抖
        AnimatedVisibility(
            visible = isLoading && !isCompactMode, // 紧凑模式下不显示加载遮罩
            enter = fadeIn(animationSpec = tween(300, delayMillis = 100)), // 稍微延迟显示，避免闪烁
            exit = fadeOut(animationSpec = tween(200))
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(animatedHeight)
                    .background(
                        color = MaterialTheme.colorScheme.surface.copy(alpha = 0.85f),
                        shape = RoundedCornerShape(25.dp) // 与Card的圆角保持一致
                    )
                    .clip(RoundedCornerShape(25.dp)),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator(
                    modifier = Modifier.size(32.dp),
                    color = MaterialTheme.colorScheme.primary,
                    strokeWidth = 3.dp
                )
            }
        }
    }
}

@Composable
private fun CompactModeContent(
    selectedDataSource: DataSource?,
    selectedBrand: String?,
    selectedHeadphone: Headphone?,
    selectedMeasurementCondition: String?,
    selectedTargetCurve: TargetCurve? = null,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        // 只显示已选择的内容，隐藏未选择的项目以实现高度自适应

        // 数据源 - 总是显示，因为这是第一步
        Text(
            text = "数据源: ${selectedDataSource?.displayName ?: "未选择"}",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurface
        )

        // 品牌 - 只有选择了才显示
        selectedBrand?.let { brand ->
            Text(
                text = "品牌: $brand",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurface
            )
        }

        // 耳机 - 只有选择了才显示
        selectedHeadphone?.let { headphone ->
            Text(
                text = "耳机: ${headphone.originalName}",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurface
            )
        }

        // 测量条件 - 只有选择了才显示
        selectedMeasurementCondition?.let { condition ->
            Text(
                text = "测量条件: $condition",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurface
            )
        }

        // 目标曲线 - 只有选择了才显示
        selectedTargetCurve?.let { curve ->
            Text(
                text = "目标曲线: ${curve.name}",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurface
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun BrandDropdown(
    brands: List<String>,
    selectedBrand: String?,
    onBrandSelected: (String) -> Unit,
    enabled: Boolean,
    expanded: Boolean,
    onExpandedChange: (Boolean) -> Unit,
    modifier: Modifier = Modifier
) {

    ExposedDropdownMenuBox(
        expanded = expanded,
        onExpandedChange = { onExpandedChange(it && enabled) },
        modifier = modifier.fillMaxWidth()
    ) {
        TextField(
            value = selectedBrand ?: "",
            onValueChange = { },
            readOnly = true,
            label = { Text("选择品牌", style = MaterialTheme.typography.bodySmall) },
            placeholder = { Text("请选择耳机品牌", style = MaterialTheme.typography.bodyMedium) },
            trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = expanded) },
            colors = TextFieldDefaults.colors(
                focusedContainerColor = MaterialTheme.colorScheme.surface,
                unfocusedContainerColor = MaterialTheme.colorScheme.surface,
                disabledContainerColor = MaterialTheme.colorScheme.surface.copy(alpha = 0.6f),
                focusedIndicatorColor = Color.Transparent,
                unfocusedIndicatorColor = Color.Transparent,
                disabledIndicatorColor = Color.Transparent,
                focusedLabelColor = MaterialTheme.colorScheme.primary,
                unfocusedLabelColor = MaterialTheme.colorScheme.onSurfaceVariant
            ),
            enabled = enabled,
            shape = RoundedCornerShape(25.dp),
            modifier = Modifier
                .fillMaxWidth()
                .height(56.dp)
                .menuAnchor()
                .border(
                    width = 1.dp,
                    color = MaterialTheme.colorScheme.outline.copy(alpha = 0.3f),
                    shape = RoundedCornerShape(25.dp)
                )
                .clip(RoundedCornerShape(25.dp)),
            textStyle = MaterialTheme.typography.bodyMedium,
            singleLine = true
        )

        MaterialTheme(
            shapes = MaterialTheme.shapes.copy(
                extraSmall = RoundedCornerShape(25.dp)
            )
        ) {
            ExposedDropdownMenu(
                expanded = expanded,
                onDismissRequest = { onExpandedChange(false) }
            ) {
                brands.forEach { brand ->
                    DropdownMenuItem(
                        text = { Text(brand, style = MaterialTheme.typography.bodyMedium) },
                        onClick = {
                            onBrandSelected(brand)
                            onExpandedChange(false)
                        }
                    )
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun HeadphoneDropdown(
    headphones: List<Headphone>,
    selectedHeadphone: Headphone?,
    onHeadphoneSelected: (Headphone) -> Unit,
    enabled: Boolean,
    expanded: Boolean,
    onExpandedChange: (Boolean) -> Unit,
    modifier: Modifier = Modifier
) {
    ExposedDropdownMenuBox(
        expanded = expanded,
        onExpandedChange = { onExpandedChange(it && enabled) },
        modifier = modifier.fillMaxWidth()
    ) {
        TextField(
            value = selectedHeadphone?.originalName ?: "",
            onValueChange = { },
            readOnly = true,
            label = { Text("选择耳机", style = MaterialTheme.typography.bodySmall) },
            placeholder = { Text("请选择耳机型号", style = MaterialTheme.typography.bodyMedium) },
            trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = expanded) },
            colors = TextFieldDefaults.colors(
                focusedContainerColor = MaterialTheme.colorScheme.surface,
                unfocusedContainerColor = MaterialTheme.colorScheme.surface,
                disabledContainerColor = MaterialTheme.colorScheme.surface.copy(alpha = 0.6f),
                focusedIndicatorColor = Color.Transparent,
                unfocusedIndicatorColor = Color.Transparent,
                disabledIndicatorColor = Color.Transparent,
                focusedLabelColor = MaterialTheme.colorScheme.primary,
                unfocusedLabelColor = MaterialTheme.colorScheme.onSurfaceVariant
            ),
            enabled = enabled,
            shape = RoundedCornerShape(25.dp),
            modifier = Modifier
                .fillMaxWidth()
                .height(56.dp)
                .menuAnchor()
                .border(
                    width = 1.dp,
                    color = MaterialTheme.colorScheme.outline.copy(alpha = 0.3f),
                    shape = RoundedCornerShape(25.dp)
                )
                .clip(RoundedCornerShape(25.dp)),
            textStyle = MaterialTheme.typography.bodyMedium,
            singleLine = true
        )

        MaterialTheme(
            shapes = MaterialTheme.shapes.copy(
                extraSmall = RoundedCornerShape(25.dp)
            )
        ) {
            ExposedDropdownMenu(
                expanded = expanded,
                onDismissRequest = { onExpandedChange(false) }
            ) {
                headphones.forEach { headphone ->
                    DropdownMenuItem(
                        text = { Text(headphone.originalName, style = MaterialTheme.typography.bodyMedium) },
                        onClick = {
                            onHeadphoneSelected(headphone)
                            onExpandedChange(false)
                        }
                    )
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun MeasurementConditionDropdown(
    conditions: List<String>,
    selectedCondition: String?,
    onConditionSelected: (String) -> Unit,
    enabled: Boolean,
    expanded: Boolean,
    onExpandedChange: (Boolean) -> Unit,
    modifier: Modifier = Modifier
) {
    ExposedDropdownMenuBox(
        expanded = expanded,
        onExpandedChange = { onExpandedChange(it && enabled) },
        modifier = modifier.fillMaxWidth()
    ) {
        TextField(
            value = selectedCondition ?: "",
            onValueChange = { },
            readOnly = true,
            label = { Text("测量条件", style = MaterialTheme.typography.bodySmall) },
            placeholder = { Text("请选择测量条件", style = MaterialTheme.typography.bodyMedium) },
            trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = expanded) },
            colors = TextFieldDefaults.colors(
                focusedContainerColor = MaterialTheme.colorScheme.surface,
                unfocusedContainerColor = MaterialTheme.colorScheme.surface,
                disabledContainerColor = MaterialTheme.colorScheme.surface.copy(alpha = 0.6f),
                focusedIndicatorColor = Color.Transparent,
                unfocusedIndicatorColor = Color.Transparent,
                disabledIndicatorColor = Color.Transparent,
                focusedLabelColor = MaterialTheme.colorScheme.primary,
                unfocusedLabelColor = MaterialTheme.colorScheme.onSurfaceVariant
            ),
            enabled = enabled,
            shape = RoundedCornerShape(25.dp),
            modifier = Modifier
                .fillMaxWidth()
                .height(56.dp)
                .menuAnchor()
                .border(
                    width = 1.dp,
                    color = MaterialTheme.colorScheme.outline.copy(alpha = 0.3f),
                    shape = RoundedCornerShape(25.dp)
                )
                .clip(RoundedCornerShape(25.dp)),
            textStyle = MaterialTheme.typography.bodyMedium,
            singleLine = true
        )

        MaterialTheme(
            shapes = MaterialTheme.shapes.copy(
                extraSmall = RoundedCornerShape(25.dp)
            )
        ) {
            ExposedDropdownMenu(
                expanded = expanded,
                onDismissRequest = { onExpandedChange(false) }
            ) {
                conditions.forEach { condition ->
                    DropdownMenuItem(
                        text = { Text(condition, style = MaterialTheme.typography.bodyMedium) },
                        onClick = {
                            onConditionSelected(condition)
                            onExpandedChange(false)
                        }
                    )
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun DataSourceDropdown(
    dataSources: List<DataSource>,
    selectedDataSource: DataSource?,
    onDataSourceSelected: (DataSource) -> Unit,
    enabled: Boolean,
    expanded: Boolean,
    onExpandedChange: (Boolean) -> Unit,
    modifier: Modifier = Modifier
) {
    ExposedDropdownMenuBox(
        expanded = expanded,
        onExpandedChange = { onExpandedChange(!expanded && enabled) },
        modifier = modifier
    ) {
        TextField(
            value = selectedDataSource?.displayName ?: "",
            onValueChange = { },
            readOnly = true,
            label = { Text("数据源") },
            placeholder = { Text("请选择数据源") },
            trailingIcon = {
                ExposedDropdownMenuDefaults.TrailingIcon(expanded = expanded)
            },
            colors = TextFieldDefaults.colors(
                focusedContainerColor = MaterialTheme.colorScheme.surface,
                unfocusedContainerColor = MaterialTheme.colorScheme.surface,
                disabledContainerColor = MaterialTheme.colorScheme.surface.copy(alpha = 0.6f),
                focusedIndicatorColor = Color.Transparent,
                unfocusedIndicatorColor = Color.Transparent,
                disabledIndicatorColor = Color.Transparent,
                focusedLabelColor = MaterialTheme.colorScheme.primary,
                unfocusedLabelColor = MaterialTheme.colorScheme.onSurfaceVariant
            ),
            enabled = enabled,
            shape = RoundedCornerShape(25.dp),
            modifier = Modifier
                .fillMaxWidth()
                .height(56.dp)
                .menuAnchor()
                .border(
                    width = 1.dp,
                    color = MaterialTheme.colorScheme.outline.copy(alpha = 0.3f),
                    shape = RoundedCornerShape(25.dp)
                )
                .clip(RoundedCornerShape(25.dp)),
            textStyle = MaterialTheme.typography.bodyMedium,
            singleLine = true
        )

        MaterialTheme(
            shapes = MaterialTheme.shapes.copy(
                extraSmall = RoundedCornerShape(25.dp)
            )
        ) {
            ExposedDropdownMenu(
                expanded = expanded,
                onDismissRequest = { onExpandedChange(false) }
            ) {
                dataSources.forEach { dataSource ->
                    DropdownMenuItem(
                        text = {
                            Column {
                                Text(
                                    text = dataSource.displayName,
                                    style = MaterialTheme.typography.bodyMedium,
                                    fontWeight = FontWeight.Medium
                                )
                                Text(
                                    text = dataSource.description,
                                    style = MaterialTheme.typography.bodySmall,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant
                                )
                            }
                        },
                        onClick = {
                            onDataSourceSelected(dataSource)
                            onExpandedChange(false)
                        }
                    )
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun TargetCurveDropdown(
    targetCurves: List<TargetCurve>,
    selectedTargetCurve: TargetCurve?,
    onTargetCurveSelected: (TargetCurve?) -> Unit,
    onTargetCurveDeleted: (TargetCurve) -> Unit,
    enabled: Boolean,
    expanded: Boolean,
    onExpandedChange: (Boolean) -> Unit,
    modifier: Modifier = Modifier
) {
    ExposedDropdownMenuBox(
        expanded = expanded,
        onExpandedChange = { onExpandedChange(it && enabled) },
        modifier = modifier.fillMaxWidth()
    ) {
        TextField(
            value = selectedTargetCurve?.name ?: "",
            onValueChange = { },
            readOnly = true,
            label = { Text("目标曲线", style = MaterialTheme.typography.bodySmall) },
            placeholder = { Text("选择目标曲线（可选）", style = MaterialTheme.typography.bodyMedium) },
            trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = expanded) },
            colors = TextFieldDefaults.colors(
                focusedContainerColor = MaterialTheme.colorScheme.surface,
                unfocusedContainerColor = MaterialTheme.colorScheme.surface,
                disabledContainerColor = MaterialTheme.colorScheme.surface.copy(alpha = 0.6f),
                focusedIndicatorColor = Color.Transparent,
                unfocusedIndicatorColor = Color.Transparent,
                disabledIndicatorColor = Color.Transparent,
                focusedLabelColor = MaterialTheme.colorScheme.primary,
                unfocusedLabelColor = MaterialTheme.colorScheme.onSurfaceVariant
            ),
            enabled = enabled,
            shape = RoundedCornerShape(25.dp),
            modifier = Modifier
                .fillMaxWidth()
                .height(56.dp)
                .menuAnchor()
                .border(
                    width = 1.dp,
                    color = MaterialTheme.colorScheme.outline.copy(alpha = 0.3f),
                    shape = RoundedCornerShape(25.dp)
                )
                .clip(RoundedCornerShape(25.dp)),
            textStyle = MaterialTheme.typography.bodyMedium,
            singleLine = true
        )

        MaterialTheme(
            shapes = MaterialTheme.shapes.copy(
                extraSmall = RoundedCornerShape(25.dp)
            )
        ) {
            ExposedDropdownMenu(
                expanded = expanded,
                onDismissRequest = { onExpandedChange(false) }
            ) {
                // 添加"无目标曲线"选项
                DropdownMenuItem(
                    text = { Text("无目标曲线", style = MaterialTheme.typography.bodyMedium) },
                    onClick = {
                        onTargetCurveSelected(null)
                        onExpandedChange(false)
                    }
                )

                targetCurves.forEach { targetCurve ->
                    DropdownMenuItem(
                        text = {
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceBetween,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Column(
                                    modifier = Modifier.weight(1f)
                                ) {
                                    Text(
                                        text = targetCurve.name,
                                        style = MaterialTheme.typography.bodyMedium,
                                        fontWeight = FontWeight.Medium
                                    )
                                    // 显示数据来源标识
                                    if (targetCurve.isLocal()) {
                                        Text(
                                            text = "本地",
                                            style = MaterialTheme.typography.bodySmall,
                                            color = MaterialTheme.colorScheme.primary
                                        )
                                    }
                                }

                                // 为本地目标曲线显示删除图标
                                if (targetCurve.isDeletable()) {
                                    IconButton(
                                        onClick = {
                                            onTargetCurveDeleted(targetCurve)
                                            onExpandedChange(false)
                                        },
                                        modifier = Modifier.size(24.dp)
                                    ) {
                                        Icon(
                                            imageVector = Icons.Default.Delete,
                                            contentDescription = "删除目标曲线",
                                            tint = MaterialTheme.colorScheme.error,
                                            modifier = Modifier.size(16.dp)
                                        )
                                    }
                                }
                            }
                        },
                        onClick = {
                            onTargetCurveSelected(targetCurve)
                            onExpandedChange(false)
                        }
                    )
                }
            }
        }
    }
}

/**
 * 操作按钮卡片组件 - 包含保存为目标曲线和FlowEq按钮
 */
@Composable
private fun ActionButtonsCard(
    canSaveAsTargetCurve: Boolean,
    canUseFlowEq: Boolean,
    isFlowEqProcessing: Boolean,
    onSaveAsTargetCurve: () -> Unit,
    onFlowEq: (cn.ykload.flowmix.data.MeasurementCondition, cn.ykload.flowmix.data.MeasurementCondition) -> Unit,
    currentMeasurementData: cn.ykload.flowmix.data.MeasurementCondition?,
    currentTargetData: cn.ykload.flowmix.data.MeasurementCondition?,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(25.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // 保存为目标曲线按钮
            Button(
                onClick = onSaveAsTargetCurve,
                enabled = canSaveAsTargetCurve,
                modifier = Modifier.fillMaxWidth(),
                colors = ButtonDefaults.buttonColors(
                    containerColor = if (canSaveAsTargetCurve) {
                        MaterialTheme.colorScheme.tertiary
                    } else {
                        MaterialTheme.colorScheme.surfaceVariant
                    },
                    contentColor = if (canSaveAsTargetCurve) {
                        MaterialTheme.colorScheme.onTertiary
                    } else {
                        MaterialTheme.colorScheme.onSurfaceVariant
                    }
                )
            ) {
                Icon(
                    imageVector = Icons.Default.Save,
                    contentDescription = null,
                    modifier = Modifier.size(18.dp),
                    tint = if (canSaveAsTargetCurve) {
                        MaterialTheme.colorScheme.onTertiary
                    } else {
                        MaterialTheme.colorScheme.onSurfaceVariant
                    }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = if (canSaveAsTargetCurve) {
                        "保存为目标曲线"
                    } else {
                        "请先选择完整的频响数据"
                    }
                )
            }

            // FlowEq按钮
            Button(
                onClick = {
                    if (canUseFlowEq && currentMeasurementData != null && currentTargetData != null) {
                        onFlowEq(currentMeasurementData, currentTargetData)
                    }
                },
                enabled = canUseFlowEq && !isFlowEqProcessing,
                modifier = Modifier.fillMaxWidth(),
                colors = ButtonDefaults.buttonColors(
                    containerColor = if (canUseFlowEq) {
                        MaterialTheme.colorScheme.secondary
                    } else {
                        MaterialTheme.colorScheme.surfaceVariant
                    },
                    contentColor = if (canUseFlowEq) {
                        MaterialTheme.colorScheme.onSecondary
                    } else {
                        MaterialTheme.colorScheme.onSurfaceVariant
                    }
                )
            ) {
                if (isFlowEqProcessing) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(18.dp),
                        strokeWidth = 2.dp,
                        color = MaterialTheme.colorScheme.onSecondary
                    )
                } else {
                    Icon(
                        imageVector = Icons.Default.AutoFixHigh,
                        contentDescription = null,
                        modifier = Modifier.size(18.dp),
                        tint = if (canUseFlowEq) {
                            MaterialTheme.colorScheme.onSecondary
                        } else {
                            MaterialTheme.colorScheme.onSurfaceVariant
                        }
                    )
                }
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = when {
                        isFlowEqProcessing -> "拟合中..."
                        canUseFlowEq -> "FlowEq 一键拟合"
                        else -> "要先选目标曲线才能拟合哦"
                    }
                )
            }
        }
    }
}


